

import logging
import subprocess
from typing import Tuple
from dataclasses import dataclass, field


@dataclass
class Process:
    args: str
    code: int
    output: str
    error: str

    @classmethod
    def from_result(self, result: subprocess.CompletedProcess):
        return Process(
            args=result.args,
            output=result.stdout.strip(),
            error=result.stderr.strip(),
            code=result.returncode,
        )

    @classmethod
    def from_init(self, args: str, code: int = 0, output: str = "", error: str = ""):
        return Process(
            args=args,
            output=output,
            error=error,
            code=code,
        )

    def is_successful(self) -> bool:
        return self.code == 0


class Grep():

    def __init__(self):
        self._base = "grep"
        self._pattern = None
        self._options = []

    def option(self, option: str):
        """指定 grep 选项, 
        可以多次指定(会自动去重), 
        也可以一次指定多个选项(无法去重)

        例: 
        option("w")  # -w
        option("iw") # -iw
        option("i").option("w")  # -iw
        option("iw").option("w")  # -iww(这种会导致选项出现重复)
        """
        self._options.append(option)
        return self

    def pattern(self, pattern: str):
        """指定 grep 规则, 只能指定一个规则, 多次调用会覆盖之前的规则
        """
        self._pattern = pattern
        return self

    def has_pattern(self) -> bool:
        """检查是否已经指定了 grep 规则
        """
        return self._pattern is not None

    def to_cmd(self):
        """生成 grep 命令
        """
        if not self._pattern:
            raise ValueError("pattern is not set")
        if self._options:
            options = list(set(self._options))
            option = ''.join(options)
            return f"{self._base} -{option} '{self._pattern}'"
        else:
            return f"{self._base} '{self._pattern}'"


class CmdExec:

    def __init__(self):
        self._namespace = None
        self.logger = logging.getLogger(__name__)

    def namespace(self, namespace: str):
        """指定命令执行的命名空间
        """
        self._namespace = namespace
        return self

    def test(self, cmd: str) -> Process:
        """测试执行命令, 只打印命令, 不执行
        """
        if self._namespace:
            cmd = f"ip netns exec {self._namespace} {cmd}"

        self.logger.info(f"Command succeeded: {cmd}")
        return Process.from_init(args=cmd)

    def exec(self, cmd: str, greps: list[Grep] = None) -> Process:
        """执行单个命令

        Args:
            task: 任务数据
            cmd: 要执行的命令

        Returns:
            Tuple of (success, output, error)
        """
        try:
            if self._namespace:
                cmd = f"ip netns exec {self._namespace} {cmd}"

            if greps:
                for grep in greps:
                    cmd += f" | {grep.to_cmd()}"

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            process = Process.from_result(result)
            if process.is_successful:
                self.logger.info(f"Command succeeded: {cmd} \nOutput: {process.output}")
            else:
                self.logger.error(f"Command failed: {cmd} \nError: {process.error}")

            return process

        except subprocess.TimeoutExpired:
            error_msg = f"Command timeout: [{cmd}]"
            self.logger.error(error_msg)

        except Exception as e:
            error_msg = f"Command [{cmd}] execution error: {e}"
            self.logger.error(error_msg)

        return Process.from_init(args=cmd, code=1, error=error_msg)


class Ip(CmdExec):

    def __init__(self):
        super().__init__()
        self._device = None
        self._target = None

    def dev(self, device: str):
        """指定设备名称
        """
        self._device = device
        return self

    def target(self, ip: str):
        """指定 IP 地址
        例:
        127.0.0.1/8
        ***********/24
        """
        self._target = ip
        return self

    def link(self) -> "IpLinkCmd":
        """指定链路命令
        """
        return IpLinkCmd()

    def addr(self) -> "IpAddrCmd":
        """指定地址命令
        """
        return IpAddrCmd()

    def route(self) -> "IpRouteCmd":
        """指定路由命令
        """
        return IpRouteCmd()

    def nft(self) -> "NftCtl":
        """指定 nftables 命令
        """
        nft_cmd = NftCtl()
        if self._namespace:
            nft_cmd.namespace(self._namespace)
        return nft_cmd


class IpLinkCmd(Ip):

    def __init__(self):
        super().__init__()
        self._base = "ip link"

    def up(self) -> Process:
        """启动设备
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} up"
        process = self.exec(cmd)
        return process

    def down(self):
        """关闭设备
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} down"
        return self.exec(cmd)

    def netns(self, namespace: str):
        """移动设备到命名空间
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} netns {namespace}"
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示设备信息, 可以指定 grep 规则来过滤结果
        """
        if self._device:
            cmd = f"{self._base} show dev {self._device}"
        else:
            cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查设备是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if device exists
        """
        if not self._device:
            raise ValueError("device is not set")
        grep = Grep().option("w").pattern(self._device)
        return self.show(grep).is_successful()


class IpAddrCmd(Ip):

    def __init__(self):
        super().__init__()
        self._base = "ip addr"

    def build(self, base_cmd: str):
        """构建命令
        """
        if self._device:
            base_cmd += f" dev {self._device}"
        return base_cmd

    def add(self) -> Process:
        """添加地址
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        cmd = self.build(f"{self._base} add {self._target}")
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除地址
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        cmd = self.build(f"{self._base} del {self._target}")
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示地址信息, 可以指定 grep 规则来过滤结果
        """
        cmd = self.build(f"{self._base} show")
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查地址是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if address exists
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        grep = Grep().option("w").pattern(self._target)
        return self.show(grep).is_successful()


class IpRouteCmd(Ip):

    def __init__(self):
        super().__init__()
        self._base = "ip route"
        self._via = None
        self._src = None
        self._metric = None

    def via(self, via: str):
        """指定网关
        """
        self._via = via
        return self

    def src(self, src: str):
        """指定源地址
        """
        self._src = src
        return self

    def metric(self, metric: int):
        """指定路由优先级
        """
        self._metric = metric
        return self

    def build(self, cmd: str):
        if self._via:
            cmd += f" via {self._via}"
        if self._device:
            cmd += f" dev {self._device}"
        if self._src:
            cmd += f" src {self._src}"
        if self._metric:
            cmd += f" metric {self._metric}"
        return cmd

    def add(self) -> Process:
        """添加路由
        """
        if not self._target:
            raise ValueError("target is not set")
        cmd = self.build(f"{self._base} add {self._target}")
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除路由
        """
        if not self._target:
            raise ValueError("target is not set")
        cmd = self.build(f"{self._base} del {self._target}")
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示路由信息, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def list(self, *greps: Grep) -> Process:
        """列出路由信息, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} list"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查路由是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if route exists
        """
        rule = self.build(self._target)
        grep = Grep().option("w").pattern(rule)
        process = self.show(grep)
        return process.is_successful()


class OvsCtl(CmdExec):
    def __init__(self):
        super().__init__()
        self._base = "ovs-vsctl"
        self._bridge = None
        self._port = None
        self._tag = None
        self._trunks = None
        self._type = None

    def bridge(self, bridge: str):
        """指定网桥名称
        """
        self._bridge = bridge
        return self

    def port(self, port: str):
        """指定端口名称
        """
        self._port = port
        return self

    def tag(self, tag: int):
        """指定端口的 VLAN 标签, 和 trunks 互斥
        """
        self._tag = tag
        return self

    def trunks(self, trunks: list[int]):
        """指定端口的 VLAN 干线, 和 tag 互斥
        trunks: list of int
        """
        self._trunks = trunks
        return self

    def type(self, type: str):
        """指定端口的类型, 如 internal, vxlan 等
        """
        self._type = type
        return self

    def type_internal(self):
        """指定端口的类型为 internal
        """
        self._type = "internal"
        return self

    def type_vxlan(self):
        """指定端口的类型为 vxlan
        """
        self._type = "vxlan"
        return self

    def show(self, *greps: Grep) -> Process:
        """显示 OVS 数据的简要概览，包括网桥、端口和管理器的信息
        """
        cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def add_bridge(self) -> Process:
        """添加网桥
        """
        cmd = f"{self._base} add-br {self._bridge}"
        return self.exec(cmd)

    def del_bridge(self) -> Process:
        """删除网桥
        """
        cmd = f"{self._base} del-br {self._bridge}"
        return self.exec(cmd)

    def build_interface(self):
        """构建端口设置命令
        """
        cmd = f"set interface {self._port}"
        if self._type:
            cmd += f" type={self._type}"
        if self._trunks:
            cmd += f" trunks={self._trunks}"
        elif self._tag:
            cmd += f" tag={self._tag}"
        return cmd

    def add_port(self, join_set: bool) -> Process:
        """添加端口, 可以指定是否将端口设置和端口添加合并为一个命令
        """
        cmd = f"{self._base} add-port"
        if self._bridge:
            cmd += f" {self._bridge}"
        else:
            raise ValueError("bridge is not set")
        if self._port:
            cmd += f" {self._port}"
        else:
            raise ValueError("port is not set")
        if join_set:
            cmd += f" -- {self.build_interface()}"
        return self.exec(cmd)

    def del_port(self) -> Process:
        """删除端口
        """
        cmd = f"{self._base} del-port"
        if self._bridge:
            cmd += f" {self._bridge}"
        else:
            raise ValueError("bridge is not set")
        if self._port:
            cmd += f" {self._port}"
        else:
            raise ValueError("port is not set")
        return self.exec(cmd)

    def set_port(self) -> Process:
        """修改端口设置
        """
        cmd = f"{self._base} set port {self._port}"
        if self._trunks:
            cmd += f" trunks={self._trunks}"
        elif self._tag:
            cmd += f" tag={self._tag}"
        else:
            raise ValueError("tag and trunks are not set")
        return self.exec(cmd)

    def clear_port(self) -> Process:
        """清除端口设置
        """
        if not self._port:
            raise ValueError("port is not set")
        cmd = f"{self._base} clear port {self._port}"
        if self._trunks:
            cmd += " trunks"
        elif self._tag:
            cmd += " tag"
        else:
            raise ValueError("tag and trunks are not set")
        return self.exec(cmd)

    def list_ports(self, *greps: Grep) -> Process:
        """列出网桥上的所有端口, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} list-ports {self._bridge}"
        return self.exec(cmd, greps)

    def check_port(self) -> bool:
        """检查端口是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if port exists
        """
        grep = Grep().option("w").pattern(self._port)
        return self.list_ports(grep).is_successful()


class NftCtl(CmdExec):
    """nftables 命令控制类"""

    def __init__(self):
        super().__init__()
        self._base = "nft"
        self._family = "ip"  # 默认为 ip 协议族
        self._table = None
        self._chain = None
        self._hook = None
        self._priority = None
        self._rule = None
        self._comment = None
        self._handle = None

    def family(self, family: str):
        """指定协议族 (ip, ip6, inet, arp, bridge, netdev)
        """
        self._family = family
        return self

    def table(self, table: str):
        """指定表名称
        """
        self._table = table
        return self

    def chain(self, chain: str):
        """指定链名称
        """
        self._chain = chain
        return self

    def hook(self, hook: str):
        """指定链钩子 (prerouting, input, forward, output, postrouting)
        """
        self._hook = hook
        return self

    def priority(self, priority: int):
        """指定链优先级
        """
        self._priority = priority
        return self

    def rule(self, rule: str):
        """指定规则内容
        """
        self._rule = rule
        return self

    def comment(self, comment: str):
        """指定规则注释
        """
        self._comment = comment
        return self

    def handle(self, handle: str):
        """指定规则句柄
        """
        self._handle = handle
        return self

    # 表操作方法
    def add_table(self) -> Process:
        """添加表
        """
        if not self._table:
            raise ValueError("table is not set")
        cmd = f"{self._base} add table {self._family} {self._table}"
        return self.exec(cmd)

    def del_table(self) -> Process:
        """删除表
        """
        if not self._table:
            raise ValueError("table is not set")
        cmd = f"{self._base} delete table {self._family} {self._table}"
        return self.exec(cmd)

    def list_tables(self, *greps: Grep) -> Process:
        """列出所有表
        """
        cmd = f"{self._base} list tables {self._family}"
        return self.exec(cmd, greps)

    def list_table(self, *greps: Grep) -> Process:
        """列出指定表的内容
        """
        if not self._table:
            raise ValueError("table is not set")
        cmd = f"{self._base} list table {self._family} {self._table}"
        return self.exec(cmd, greps)

    def flush_table(self) -> Process:
        """清空表中的所有规则
        """
        if not self._table:
            raise ValueError("table is not set")
        cmd = f"{self._base} flush table {self._family} {self._table}"
        return self.exec(cmd)

    def check_table(self) -> bool:
        """检查表是否存在

        Returns:
            True if table exists
        """
        if not self._table:
            raise ValueError("table is not set")
        grep = Grep().option("w").pattern(self._table)
        return self.list_tables(grep).is_successful()

    # 链操作方法
    def add_chain(self) -> Process:
        """添加链
        """
        if not self._table or not self._chain:
            raise ValueError("table and chain must be set")

        cmd = f"{self._base} add chain {self._family} {self._table} {self._chain}"

        # 如果指定了钩子，则创建基础链
        if self._hook:
            priority = self._priority if self._priority is not None else self._get_default_priority()
            cmd += f" {{ type nat hook {self._hook} priority {priority} \\; }}"

        return self.exec(cmd)

    def del_chain(self) -> Process:
        """删除链
        """
        if not self._table or not self._chain:
            raise ValueError("table and chain must be set")
        cmd = f"{self._base} delete chain {self._family} {self._table} {self._chain}"
        return self.exec(cmd)

    def list_chain(self, *greps: Grep) -> Process:
        """列出指定链的内容
        """
        if not self._table or not self._chain:
            raise ValueError("table and chain must be set")
        cmd = f"{self._base} list chain {self._family} {self._table} {self._chain}"
        return self.exec(cmd, greps)

    def flush_chain(self) -> Process:
        """清空链中的所有规则
        """
        if not self._table or not self._chain:
            raise ValueError("table and chain must be set")
        cmd = f"{self._base} flush chain {self._family} {self._table} {self._chain}"
        return self.exec(cmd)

    def check_chain(self) -> bool:
        """检查链是否存在

        Returns:
            True if chain exists
        """
        if not self._table or not self._chain:
            raise ValueError("table and chain must be set")
        grep = Grep().option("w").pattern(f"chain {self._chain}")
        return self.list_table(grep).is_successful()

    # 规则操作方法
    def add_rule(self) -> Process:
        """添加规则
        """
        if not self._table or not self._chain or not self._rule:
            raise ValueError("table, chain and rule must be set")

        cmd = f"{self._base} add rule {self._family} {self._table} {self._chain} {self._rule}"

        if self._comment:
            cmd += f" comment \"{self._comment}\""

        return self.exec(cmd)

    def del_rule(self) -> Process:
        """删除规则 (通过句柄)
        """
        if not self._table or not self._chain or not self._handle:
            raise ValueError("table, chain and handle must be set")
        cmd = f"{self._base} delete rule {self._family} {self._table} {self._chain} handle {self._handle}"
        return self.exec(cmd)

    def del_rule_by_comment(self) -> Process:
        """通过注释删除规则
        """
        if not self._table or not self._chain or not self._comment:
            raise ValueError("table, chain and comment must be set")

        # 首先获取规则句柄
        list_cmd = f"{self._base} -a list chain {self._family} {self._table} {self._chain}"
        grep_cmd = f"grep 'comment \"{self._comment}\"' | awk '{{print $NF}}'"

        # 执行命令获取句柄
        full_cmd = f"{list_cmd} | {grep_cmd}"
        result = self.exec(full_cmd)

        if result.is_successful() and result.output.strip():
            handle = result.output.strip()
            cmd = f"{self._base} delete rule {self._family} {self._table} {self._chain} handle {handle}"
            return self.exec(cmd)

        # 如果没有找到规则，返回成功（规则已经不存在）
        return Process.from_init(args=full_cmd, code=0, output="Rule not found, already deleted")

    def list_ruleset(self, *greps: Grep) -> Process:
        """列出所有规则集
        """
        cmd = f"{self._base} list ruleset"
        return self.exec(cmd, greps)

    def flush_ruleset(self) -> Process:
        """清空所有规则集
        """
        cmd = f"{self._base} flush ruleset"
        return self.exec(cmd)

    def check_rule(self) -> bool:
        """检查规则是否存在 (通过注释)

        Returns:
            True if rule exists
        """
        if not self._comment:
            raise ValueError("comment must be set for rule checking")
        grep = Grep().pattern(f'comment "{self._comment}"')
        return self.list_ruleset(grep).is_successful()

    def _get_default_priority(self) -> int:
        """根据钩子类型获取默认优先级
        """
        if self._hook == "prerouting":
            return -100
        elif self._hook == "postrouting":
            return 100
        else:
            return 0

    # 便捷方法
    def nat_table(self, vlan_id: int):
        """设置 NAT 表名称 (格式: nat-{vlan_id})
        """
        return self.table(f"nat-{vlan_id}")

    def prerouting_chain(self, name: str, priority: int = -100):
        """设置 prerouting 链
        """
        return self.chain(name).hook("prerouting").priority(priority)

    def postrouting_chain(self, name: str, priority: int = 100):
        """设置 postrouting 链
        """
        return self.chain(name).hook("postrouting").priority(priority)

    def snat_rule(self, src_addr: str, target_addr: str, comment: str = None):
        """创建 SNAT 规则

        Args:
            src_addr: 源地址，如 "***********/24"
            target_addr: 目标地址，如 "********"
            comment: 规则注释
        """
        rule_text = f"ip saddr {src_addr} counter snat to {target_addr}"
        self._rule = rule_text
        if comment:
            self._comment = comment
        return self

    def dnat_rule(self, dst_addr: str, target_addr: str, comment: str = None):
        """创建 DNAT 规则

        Args:
            dst_addr: 目标地址，如 "********"
            target_addr: 转换后的地址，如 "***********00"
            comment: 规则注释
        """
        rule_text = f"ip daddr {dst_addr} counter dnat to {target_addr}"
        self._rule = rule_text
        if comment:
            self._comment = comment
        return self

    def port_dnat_rule(self, iface: str, protocol: str, port: int, target_ip: str, target_port: int, comment: str = None):
        """创建端口 DNAT 规则

        Args:
            iface: 接口名称
            protocol: 协议 (tcp/udp)
            port: 源端口
            target_ip: 目标IP
            target_port: 目标端口
            comment: 规则注释
        """
        rule_text = f"iifname {iface} {protocol} dport {port} dnat to {target_ip}:{target_port}"
        self._rule = rule_text
        if comment:
            self._comment = comment
        return self


if __name__ == "__main__":
    # 测试 NftCtl 类
    print("=== Testing NftCtl class ===")

    # 测试表操作
    nft = NftCtl().table("test_table")
    print("Add table command:", nft.add_table().args)
    print("List tables command:", nft.list_tables().args)

    # 测试链操作
    nft_chain = NftCtl().table("nat-100").chain("postrouting_test").hook("postrouting").priority(100)
    print("Add chain command:", nft_chain.add_chain().args)
    print("List chain command:", nft_chain.list_chain().args)

    # 测试规则操作
    nft_rule = NftCtl().table("nat-100").chain("postrouting_test").rule("ip saddr ***********/24 counter snat to ********").comment("test_rule")
    print("Add rule command:", nft_rule.add_rule().args)

    # 测试命名空间
    nft_ns = NftCtl().namespace("ns-vlan100").table("nat-100").chain("postrouting_test")
    print("Namespace command:", nft_ns.list_chain().args)

    print("\n=== Testing convenience methods ===")
    # 测试便捷方法
    nft_conv = NftCtl().nat_table(100).postrouting_chain("postrouting_eip", 101)
    print("NAT table + postrouting chain:", nft_conv.add_chain().args)

    # 测试 SNAT 规则
    snat_rule = NftCtl().nat_table(100).postrouting_chain("postrouting_eip").snat_rule("***********/24", "********", "subnet_snat")
    print("SNAT rule command:", snat_rule.add_rule().args)

    # 测试 DNAT 规则
    dnat_rule = NftCtl().nat_table(100).prerouting_chain("prerouting_eip", -99).dnat_rule("********", "***********00", "eip_dnat")
    print("DNAT rule command:", dnat_rule.add_rule().args)

    # 测试端口 DNAT 规则
    port_dnat = NftCtl().nat_table(100).prerouting_chain("prerouting_eip").port_dnat_rule("v-eip-ns-100", "tcp", 8080, "***********00", 80, "port_dnat")
    print("Port DNAT rule command:", port_dnat.add_rule().args)

    print("\n=== Testing integration with Ip class ===")
    # 测试与 Ip 类的集成
    ip_nft = Ip().namespace("ns-vlan100").nft().nat_table(100).postrouting_chain("postrouting_sub")
    print("Integrated IP + NFT command:", ip_nft.add_chain().args)

    # 在命名空间中添加 SNAT 规则
    ip_snat = Ip().namespace("ns-vlan100").nft().nat_table(100).postrouting_chain("postrouting_sub").snat_rule("*************/24", "********00", "subnet_snat_100")
    print("Namespace SNAT rule:", ip_snat.add_rule().args)

    print("\n=== Testing existing classes ===")
    # 原有的测试代码
    process = Ip().link().show()
    print("IP link show command:", process.args)
