

import logging
import subprocess
from typing import Tuple
from dataclasses import dataclass, field


@dataclass
class Process:
    args: str
    code: int
    output: str
    error: str

    @classmethod
    def from_result(self, result: subprocess.CompletedProcess):
        return Process(
            args=result.args,
            output=result.stdout.strip(),
            error=result.stderr.strip(),
            code=result.returncode,
        )

    @classmethod
    def from_init(self, args: str, code: int = 0, output: str = "", error: str = ""):
        return Process(
            args=args,
            output=output,
            error=error,
            code=code,
        )

    def is_successful(self) -> bool:
        return self.code == 0


class Grep():

    def __init__(self):
        self._base = "grep"
        self._pattern = None
        self._options = []

    def option(self, option: str):
        """指定 grep 选项, 
        可以多次指定(会自动去重), 
        也可以一次指定多个选项(无法去重)

        例: 
        option("w")  # -w
        option("iw") # -iw
        option("i").option("w")  # -iw
        option("iw").option("w")  # -iww(这种会导致选项出现重复)
        """
        self._options.append(option)
        return self

    def pattern(self, pattern: str):
        """指定 grep 规则, 只能指定一个规则, 多次调用会覆盖之前的规则
        """
        self._pattern = pattern
        return self

    def has_pattern(self) -> bool:
        """检查是否已经指定了 grep 规则
        """
        return self._pattern is not None

    def to_cmd(self):
        """生成 grep 命令
        """
        if not self._pattern:
            raise ValueError("pattern is not set")
        if self._options:
            options = list(set(self._options))
            option = ''.join(options)
            return f"{self._base} -{option} '{self._pattern}'"
        else:
            return f"{self._base} '{self._pattern}'"


class CmdExec:

    def __init__(self):
        self._namespace = None
        self.logger = logging.getLogger(__name__)

    def namespace(self, namespace: str):
        """指定命令执行的命名空间
        """
        self._namespace = namespace
        return self

    def test(self, cmd: str) -> Process:
        """测试执行命令, 只打印命令, 不执行
        """
        if self._namespace:
            cmd = f"ip netns exec {self._namespace} {cmd}"

        self.logger.info(f"Command succeeded: {cmd}")
        return Process.from_init(args=cmd)

    def exec(self, cmd: str, greps: list[Grep] = None) -> Process:
        """执行单个命令

        Args:
            task: 任务数据
            cmd: 要执行的命令

        Returns:
            Tuple of (success, output, error)
        """
        try:
            if self._namespace:
                cmd = f"ip netns exec {self._namespace} {cmd}"

            for grep in greps:
                cmd += f" | {grep.to_cmd()}"

            result = subprocess.run(
                cmd,
                shell=True,
                capture_output=True,
                text=True,
                timeout=30
            )
            process = Process.from_result(result)
            if process.is_successful:
                self.logger.info(f"Command succeeded: {cmd} \nOutput: {process.output}")
            else:
                self.logger.error(f"Command failed: {cmd} \nError: {process.error}")

            return process

        except subprocess.TimeoutExpired:
            error_msg = f"Command timeout: [{cmd}]"
            self.logger.error(error_msg)

        except Exception as e:
            error_msg = f"Command [{cmd}] execution error: {e}"
            self.logger.error(error_msg)

        return Process.from_init(args=cmd, code=1, error=error_msg)


class Ip(CmdExec):

    def __init__(self):
        super().__init__()
        self._device = None
        self._target = None

    def dev(self, device: str):
        """指定设备名称
        """
        self._device = device
        return self

    def target(self, ip: str):
        """指定 IP 地址
        例:
        127.0.0.1/8
        ***********/24
        """
        self._target = ip
        return self

    def link(self) -> "IpLinkCmd":
        """指定链路命令
        """
        return IpLinkCmd()

    def addr(self) -> "IpAddrCmd":
        """指定地址命令
        """
        return IpAddrCmd()

    def route(self) -> "IpRouteCmd":
        """指定路由命令
        """
        return IpRouteCmd()


class IpLinkCmd(Ip):

    def __init__(self):
        super().__init__()
        self._base = "ip link"

    def up(self) -> Process:
        """启动设备
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} up"
        process = self.exec(cmd)
        return process

    def down(self):
        """关闭设备
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} down"
        return self.exec(cmd)

    def netns(self, namespace: str):
        """移动设备到命名空间
        """
        if not self._device:
            raise ValueError("device is not set")
        cmd = f"{self._base} set {self._device} netns {namespace}"
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示设备信息, 可以指定 grep 规则来过滤结果
        """
        if self._device:
            cmd = f"{self._base} show dev {self._device}"
        else:
            cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查设备是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if device exists
        """
        if not self._device:
            raise ValueError("device is not set")
        grep = Grep().option("w").pattern(self._device)
        return self.show(grep).is_successful()


class IpAddrCmd(Ip):

    def __init__(self):
        super().__init__()
        self._base = "ip addr"

    def build(self, base_cmd: str):
        """构建命令
        """
        if self._device:
            base_cmd += f" dev {self._device}"
        return base_cmd

    def add(self) -> Process:
        """添加地址
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        cmd = self.build(f"{self._base} add {self._target}")
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除地址
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        cmd = self.build(f"{self._base} del {self._target}")
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示地址信息, 可以指定 grep 规则来过滤结果
        """
        cmd = self.build(f"{self._base} show")
        return self.exec(cmd, greps)
    
    def check(self) -> bool:
        """检查地址是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if address exists
        """
        if not self._target:
            raise ValueError("target is not set")
        if not self._device:
            raise ValueError("device is not set")
        grep = Grep().option("w").pattern(self._target)
        return self.show(grep).is_successful()


class IpRouteCmd(Ip):

    def __init__(self):
        super().__init__()
        self._base = "ip route"
        self._via = None
        self._src = None
        self._metric = None

    def via(self, via: str):
        """指定网关
        """
        self._via = via
        return self

    def src(self, src: str):
        """指定源地址
        """
        self._src = src
        return self

    def metric(self, metric: int):
        """指定路由优先级
        """
        self._metric = metric
        return self

    def build(self, cmd: str):
        if self._via:
            cmd += f" via {self._via}"
        if self._device:
            cmd += f" dev {self._device}"
        if self._src:
            cmd += f" src {self._src}"
        if self._metric:
            cmd += f" metric {self._metric}"
        return cmd

    def add(self) -> Process:
        """添加路由
        """
        if not self._target:
            raise ValueError("target is not set")
        cmd = self.build(f"{self._base} add {self._target}")
        return self.exec(cmd)

    def delete(self) -> Process:
        """删除路由
        """
        if not self._target:
            raise ValueError("target is not set")
        cmd = self.build(f"{self._base} del {self._target}")
        return self.exec(cmd)

    def show(self, *greps: Grep) -> Process:
        """显示路由信息, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def list(self, *greps: Grep) -> Process:
        """列出路由信息, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} list"
        return self.exec(cmd, greps)

    def check(self) -> bool:
        """检查路由是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if route exists
        """
        rule = self.build(self._target)
        grep = Grep().option("w").pattern(rule)
        process = self.show(grep)
        return process.is_successful()


class OvsCtl(CmdExec):
    def __init__(self):
        super().__init__()
        self._base = "ovs-vsctl"
        self._bridge = None
        self._port = None
        self._tag = None
        self._trunks = None
        self._type = None

    def bridge(self, bridge: str):
        """指定网桥名称
        """
        self._bridge = bridge
        return self

    def port(self, port: str):
        """指定端口名称
        """
        self._port = port
        return self

    def tag(self, tag: int):
        """指定端口的 VLAN 标签, 和 trunks 互斥
        """
        self._tag = tag
        return self

    def trunks(self, trunks: list[int]):
        """指定端口的 VLAN 干线, 和 tag 互斥
        trunks: list of int
        """
        self._trunks = trunks
        return self

    def type(self, type: str):
        """指定端口的类型, 如 internal, vxlan 等
        """
        self._type = type
        return self

    def type_internal(self):
        """指定端口的类型为 internal
        """
        self._type = "internal"
        return self

    def type_vxlan(self):
        """指定端口的类型为 vxlan
        """
        self._type = "vxlan"
        return self

    def show(self, *greps: Grep) -> Process:
        """显示 OVS 数据的简要概览，包括网桥、端口和管理器的信息
        """
        cmd = f"{self._base} show"
        return self.exec(cmd, greps)

    def add_bridge(self) -> Process:
        """添加网桥
        """
        cmd = f"{self._base} add-br {self._bridge}"
        return self.exec(cmd)

    def del_bridge(self) -> Process:
        """删除网桥
        """
        cmd = f"{self._base} del-br {self._bridge}"
        return self.exec(cmd)

    def build_interface(self):
        """构建端口设置命令
        """
        cmd = f"set interface {self._port}"
        if self._type:
            cmd += f" type={self._type}"
        if self._trunks:
            cmd += f" trunks={self._trunks}"
        elif self._tag:
            cmd += f" tag={self._tag}"
        return cmd

    def add_port(self, join_set: bool) -> Process:
        """添加端口, 可以指定是否将端口设置和端口添加合并为一个命令
        """
        cmd = f"{self._base} add-port"
        if self._bridge:
            cmd += f" {self._bridge}"
        else:
            raise ValueError("bridge is not set")
        if self._port:
            cmd += f" {self._port}"
        else:
            raise ValueError("port is not set")
        if join_set:
            cmd += f" -- {self.build_interface()}"
        return self.exec(cmd)

    def del_port(self) -> Process:
        """删除端口
        """
        cmd = f"{self._base} del-port"
        if self._bridge:
            cmd += f" {self._bridge}"
        else:
            raise ValueError("bridge is not set")
        if self._port:
            cmd += f" {self._port}"
        else:
            raise ValueError("port is not set")
        return self.exec(cmd)

    def set_port(self) -> Process:
        """修改端口设置
        """
        cmd = f"{self._base} set port {self._port}"
        if self._trunks:
            cmd += f" trunks={self._trunks}"
        elif self._tag:
            cmd += f" tag={self._tag}"
        else:
            raise ValueError("tag and trunks are not set")
        return self.exec(cmd)
    
    def clear_port(self) -> Process:
        """清除端口设置
        """
        if not self._port:
            raise ValueError("port is not set")
        cmd = f"{self._base} clear port {self._port}"
        if self._trunks:
            cmd += " trunks"
        elif self._tag:
            cmd += " tag"
        else:
            raise ValueError("tag and trunks are not set")
        return self.exec(cmd)

    def list_ports(self, *greps: Grep) -> Process:
        """列出网桥上的所有端口, 可以指定 grep 规则来过滤结果
        """
        cmd = f"{self._base} list-ports {self._bridge}"
        return self.exec(cmd, greps)

    def check_port(self) -> bool:
        """检查端口是否存在, 会根据当前设置的参数自动生成 grep 规则

        Returns:
            True if port exists
        """
        grep = Grep().option("w").pattern(self._port)
        return self.list_ports(grep).is_successful()


if __name__ == "__main__":
    # grep = Grep().option("w").option("w").pattern("LOWER_UP")
    process = Ip().link().show()

    # cmd = OvsCtl().bridge("br-eip").port("v-eip-host-1003")

    # process = cmd.add_port(True)
    # print(process)
    # print(grep.to_cmd())

    print(process.args)
    # print(process.is_successful())
    print(process.output)
    # print(process.error)
